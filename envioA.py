import requests
import os
import time
import json
import math
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

class PostfixCampaignSender:
    def __init__(self, base_urls=None):
        # Carrega URLs do arquivo entregues.txt
        if base_urls is None:
            try:
                with open('entregues.txt', 'r') as f:
                    base_urls = [f"https://{line.strip()}.fly.dev" for line in f if line.strip()]
            except FileNotFoundError:
                base_urls = []
        self.base_urls = base_urls if isinstance(base_urls, list) else [base_urls]
        self.sessions = [requests.Session() for _ in self.base_urls]
        for session in self.sessions:
            session.headers.update({
                'User-Agent': 'PostfixCampaignSender/1.0',
                'Accept': 'application/json'
            })
    
    def check_server_status(self):
        """Verifica o status de todos os servidores Postfix"""
        def check_single_server(session, base_url):
            try:
                response = session.get(f"{base_url}/postfix-status", timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    print(f"Status do Postfix ({base_url}): {data.get('emailStatus', 'desconhecido')}")
                    return True
                else:
                    print(f"Erro ao verificar status do servidor {base_url}: {response.status_code}")
                    return False
            except Exception as e:
                print(f"Falha ao conectar com o servidor {base_url}: {str(e)}")
                return False

        with ThreadPoolExecutor() as executor:
            results = list(executor.map(
                check_single_server,
                self.sessions,
                self.base_urls
            ))
        
        active_servers = sum(results)
        print(f"\nResumo: {active_servers}/{len(self.base_urls)} servidores ativos")
        
        # Continua se pelo menos um servidor estiver ativo
        return active_servers > 0
    
    def upload_html(self, html_file_path):
        """Faz upload do arquivo HTML para todos servidores"""
        if not os.path.exists(html_file_path):
            print(f"Arquivo HTML não encontrado: {html_file_path}")
            return False
        
        def upload_to_server(session, base_url):
            try:
                with open(html_file_path, 'rb') as f:
                    files = {'html': (os.path.basename(html_file_path), f, 'text/html')}
                    response = session.post(
                        f"{base_url}/upload-html",
                        files=files,
                        timeout=60
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        print(f"HTML enviado para {base_url}: {data.get('message', '')}")
                        return True
                    else:
                        print(f"Erro no upload para {base_url}: {response.status_code}")
                        print(f"Resposta: {response.text}")
                        return False
            except Exception as e:
                print(f"Falha no upload para {base_url}: {str(e)}")
                return False

        with ThreadPoolExecutor() as executor:
            results = list(executor.map(
                upload_to_server,
                self.sessions,
                self.base_urls
            ))
        
        # Retorna True se pelo menos um upload foi bem sucedido
        return any(results)
    
    def upload_email_list(self, email_list_path, list_type="nome,email,cpf"):
        """
        Faz upload da lista de emails dividindo entre os servidores disponíveis
        Mantém o cabeçalho em cada parte da lista dividida
        """
        if not os.path.exists(email_list_path):
            print(f"Arquivo de lista de emails não encontrado: {email_list_path}")
            return False

        # Lê todo o conteúdo do arquivo
        with open(email_list_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) < 2:  # Cabeçalho + pelo menos 1 linha
            print("Lista de emails vazia ou incompleta")
            return False

        header = lines[0]
        data_lines = lines[1:]
        num_servers = len(self.base_urls)
        total_lines = len(data_lines)
        chunk_size = total_lines // num_servers
        remainder = total_lines % num_servers

        print(f"Dividindo lista de {total_lines} linhas em {num_servers} servidores")
        print(f"Tamanho base de cada parte: {chunk_size} linhas")
        print(f"Servidores que receberão 1 linha extra: {remainder}")

        # Divide os emails em partes mantendo o cabeçalho
        temp_files = []
        try:
            line_idx = 0
            for i in range(num_servers):
                # Calcula tamanho deste chunk (base + 1 extra se houver remainder)
                this_chunk_size = chunk_size + (1 if i < remainder else 0)
                if this_chunk_size == 0:
                    continue
                    
                chunk = data_lines[line_idx:line_idx+this_chunk_size]
                line_idx += this_chunk_size
                
                temp_path = f"{email_list_path}.part{i+1}.txt"
                with open(temp_path, 'w', encoding='utf-8') as f_out:
                    f_out.write(header)
                    f_out.writelines(chunk)
                
                print(f"Parte {i+1}: {len(chunk)} linhas -> {temp_path}")
                temp_files.append(temp_path)

            # Faz upload paralelo para todos os servidores
            def upload_part(session, base_url, part_path):
                try:
                    with open(part_path, 'rb') as f:
                        files = {'emails': (os.path.basename(part_path), f, 'text/plain')}
                        response = session.post(
                            f"{base_url}/upload-list?list_type={list_type}",
                            files=files,
                            timeout=300
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            print(f"Lista enviada para {base_url}: {data.get('message', '')}")
                            print(f"Tipo detectado: {data.get('details', {}).get('listType', 'desconhecido')}")
                            print(f"Entradas: {data.get('details', {}).get('entries', 0)}")
                            return True
                        else:
                            print(f"Erro no upload para {base_url}: {response.status_code}")
                            print(f"Resposta: {response.text}")
                            return False
                except Exception as e:
                    print(f"Falha no upload para {base_url}: {str(e)}")
                    return False

            with ThreadPoolExecutor() as executor:
                results = list(executor.map(
                    upload_part,
                    self.sessions,
                    self.base_urls,
                    temp_files
                ))
            return all(results)

        finally:
            # Remove arquivos temporários
            for temp_path in temp_files:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
    
    def start_campaign(self, senders, subjects, max_retries=3):
        """Inicia a campanha em todos os servidores com os remetentes e assuntos especificados"""
        if not isinstance(senders, list) or len(senders) == 0:
            print("Forneça pelo menos um remetente")
            return []
        
        if not isinstance(subjects, list) or len(subjects) == 0:
            print("Forneça pelo menos um assunto")
            return []
        
        payload = {
            "senders": senders,
            "subjects": subjects
        }
        
        campaign_ids = []
        
        def start_on_server(session, base_url):
            for attempt in range(max_retries):
                try:
                    response = session.post(
                        f"{base_url}/start-campaign",
                        json=payload,
                        timeout=60
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        campaign_id = data.get('campaignId')
                        print(f"Campanha iniciada em {base_url}! ID: {campaign_id}")
                        print(f"Detalhes: {json.dumps(data.get('details', {}), indent=2)}")
                        
                        # Registrar no arquivo de log
                        with open('campaign_ids.log', 'a') as f:
                            f.write(f"{base_url},{campaign_id},{datetime.now().isoformat()}\n")
                        
                        return campaign_id
                    else:
                        print(f"Erro ao iniciar campanha em {base_url} (tentativa {attempt + 1}/{max_retries}): {response.status_code}")
                        print(f"Resposta: {response.text}")
                        if attempt < max_retries - 1:
                            time.sleep(5)  # Espera antes de tentar novamente
                except Exception as e:
                    print(f"Falha ao iniciar campanha em {base_url} (tentativa {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt < max_retries - 1:
                        time.sleep(5)  # Espera antes de tentar novamente
            
            print(f"Falha ao iniciar campanha em {base_url} após {max_retries} tentativas")
            return None

        with ThreadPoolExecutor() as executor:
            campaign_ids = list(executor.map(
                start_on_server,
                self.sessions,
                self.base_urls
            ))
        
        # Retorna apenas os IDs que foram obtidos com sucesso
        return [cid for cid in campaign_ids if cid is not None]
    
    async def monitor_campaign_async(self, campaign_ids=None, interval=5, log_failures_to=None, max_retries=3):
        """Monitora o progresso de campanhas em todos servidores de forma assíncrona"""
        import asyncio
        import aiohttp
        
        # Se nenhum ID for fornecido, tenta ler do arquivo de log
        if campaign_ids is None:
            try:
                with open('campaign_ids.log', 'r') as f:
                    campaign_ids = []
                    for line in f:
                        if line.startswith('#'):
                            continue
                        parts = line.strip().split(',')
                        if len(parts) >= 2:
                            campaign_ids.append(parts[1])
            except FileNotFoundError:
                print("Arquivo campaign_ids.log não encontrado")
                return False
        
        if not campaign_ids:
            print("Nenhum ID de campanha disponível para monitoramento")
            return False
        
        print(f"\nMonitorando assincronamente campanhas {campaign_ids}...")
        print("Pressione Ctrl+C para parar o monitoramento\n")
        
        async def check_campaign_status(session, base_url, campaign_id):
            for attempt in range(max_retries):
                try:
                    async with session.get(
                        f"{base_url}/campanha/{campaign_id}",
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        
                        if response.status == 200:
                            data = await response.json()
                            stats = data.get('estatisticas', {})
                            return {
                                'base_url': base_url,
                                'campaign_id': campaign_id,
                                'status': data.get('status', 'desconhecido'),
                                'sent': stats.get('enviados', 0),
                                'failed': stats.get('falhas', 0),
                                'pending': stats.get('pendentes', 0),
                                'total': stats.get('total', 0),
                                'failures': data.get('falhas_detalhes', [])
                            }
                        else:
                            print(f"Erro ao obter status da campanha {campaign_id} em {base_url} (tentativa {attempt + 1}/{max_retries}): {response.status}")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(5)
                except Exception as e:
                    print(f"Erro ao monitorar campanha {campaign_id} em {base_url} (tentativa {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(5)
            return None
        
        try:
            async with aiohttp.ClientSession(headers={
                'User-Agent': 'PostfixCampaignSender/1.0',
                'Accept': 'application/json'
            }) as session:
                
                while True:
                    tasks = []
                    for i, base_url in enumerate(self.base_urls):
                        if i >= len(campaign_ids) or not campaign_ids[i]:
                            continue
                        tasks.append(check_campaign_status(session, base_url, campaign_ids[i]))
                    
                    # Executa todas as verificações simultaneamente
                    results = await asyncio.gather(*tasks)
                    
                    total_sent = 0
                    total_failed = 0
                    total_pending = 0
                    active_servers = 0
                    
                    for result in results:
                        if result is None:
                            continue
                            
                        total_sent += result['sent']
                        total_failed += result['failed']
                        total_pending += result['pending']
                        active_servers += 1
                        
                        # Exibir detalhes do servidor atual
                        print(f"\nServidor: {result['base_url']}")
                        print(f"Campanha ID: {result['campaign_id']}")
                        print(f"Status: {result['status']}")
                        print(f"Enviados: {result['sent']} | Falhas: {result['failed']} | Pendentes: {result['pending']}")
                        print(f"Progresso: {(result['sent']/result['total'])*100:.2f}%")
                        
                        # Registrar falhas
                        if log_failures_to and result['failed'] > 0:
                            with open(log_failures_to, 'a') as f:
                                if not result['failures']:
                                    f.write(f"unknown,unknown,Falha - Email retornou - {result['base_url']}\n")
                                else:
                                    for fail in result['failures']:
                                        f.write(f"{fail.get('email','unknown')},{fail.get('cpf','unknown')},{fail.get('motivo','Falha - Email retornou')}\n")
                    
                    total_emails = total_sent + total_failed + total_pending
                    progress = (total_sent / total_emails) * 100 if total_emails > 0 else 0
                    
                    print(f"\n{'='*50}")
                    print(f"Status geral ({active_servers}/{len(self.base_urls)} servidores ativos):")
                    print(f"Progresso total: {progress:.2f}%")
                    print(f"Enviados: {total_sent} | Falhas: {total_failed} | Pendentes: {total_pending}")
                    print(f"Última atualização: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"{'='*50}")
                    
                    if total_pending == 0:
                        print("\nTodas campanhas finalizadas!")
                        return True
                    
                    await asyncio.sleep(interval)
        
        except KeyboardInterrupt:
            print("\nMonitoramento interrompido pelo usuário")
            return True
        except Exception as e:
            print(f"Erro no monitoramento: {str(e)}")
            return False

    def monitor_campaign(self, campaign_ids=None, interval=5, log_failures_to=None, max_retries=3):
        """Wrapper síncrono para o monitoramento assíncrono"""
        import asyncio
        return asyncio.run(self.monitor_campaign_async(campaign_ids, interval, log_failures_to, max_retries))

if __name__ == "__main__":
    # Configurações da campanha
    HTML_FILE = "CPF.html"  # Substitua pelo seu arquivo HTML
    EMAIL_LIST = "emails.txt"    # Substitua pela sua lista de emails
    
    # Lista de remetentes
    SENDERS = [
        "Feirao Serasa no-reply@serasa",
        "Feirao Serasa regularizacao@serasa", 
        "Feirao Serasa suporte@serasa",
        "Feirao Serasa bloqueio@serasa",
        "Feirao Serasa aviso@serasa",
        "Feirao Serasa restricoes@serasa",
        "Feirao Serasa bloqueioimediato@serasa",
        "Feirao Serasa acaojudicial@serasa"
    ]
    
    # Lista de assuntos (usar %cpf% para substituir pelo CPF)
    SUBJECTS = [
        "🚨 Alerta: Nova dívida registrada em seu nome!",
        "⛔️ ATENÇÃO: Seu CPF será bloqueado em 24 horas!",
        "🚫 IGNOROU? Sua dívida pode virar restrição judicial!",
        "💥 URGENTE: Regularize agora ou perca acesso a crédito!",
        "🔥 BLOQUEIO IMINENTE: Seu CPF será bloqueado em 12 horas!",
    ]
    
    # Criar instância do sender com todos servidores
    sender = PostfixCampaignSender()
    
    # Verificar status do servidor
    if not sender.check_server_status():
        exit(1)
    
    # Fazer upload do HTML
    print("\nEnviando arquivo HTML...")
    if not sender.upload_html(HTML_FILE):
        exit(1)
    
    # Fazer upload da lista de emails
    print("\nEnviando lista de emails...")
    if not sender.upload_email_list(EMAIL_LIST):
        exit(1)
    
    # Iniciar a campanha
    print("\nIniciando campanha...")
    campaign_ids = sender.start_campaign(SENDERS, SUBJECTS)
    if not campaign_ids:
        exit(1)
    
    # Monitorar o progresso da campanha e registrar falhas
    print("\nIniciando monitoramento assíncrono das campanhas...")
    sender.monitor_campaign(campaign_ids, interval=5, log_failures_to="falhas.log")
