import asyncio
import aiohttp
import aiofiles
import json
import time
import re
import os
from bs4 import BeautifulSoup
from datetime import datetime

# Configurações do 2captcha
API_KEY = "104e4275728d77f04d6c2bf11a93538d"  # Sua chave de API do 2captcha
SITE_KEY = "6LetGEImAAAAAIji8OYruian_LlJwIW9E3ZC-0ps"  # Site key do reCAPTCHA
PAGE_URL = "https://consultanumero.abrtelecom.com.br/consultanumero/consulta/consultaSituacaoAtualCtg"
ARQUIVO_NUMEROS = "numeros.txt"  # Arquivo com a lista de números
DIRETORIO_RESULTADOS = "resultados"  # Diretório para salvar os resultados
MAX_CONCURRENT_TASKS = 5  # Número máximo de tarefas concorrentes
RESULTADOS_PROCESSADOS = set()  # Conjunto para evitar duplicatas

def formatar_numero(numero):
    """Formata o número para o padrão aceito pelo site (XX) XXXXX-XXXX"""
    # Remove todos os caracteres não numéricos
    numero_limpo = re.sub(r'\D', '', numero)

    # Verifica se o número tem o tamanho correto (11 dígitos para celular)
    if len(numero_limpo) == 11:
        return f"({numero_limpo[:2]}) {numero_limpo[2:7]}-{numero_limpo[7:]}"
    # 10 dígitos para telefone fixo
    elif len(numero_limpo) == 10:
        return f"({numero_limpo[:2]}) {numero_limpo[2:6]}-{numero_limpo[6:]}"
    else:
        return numero  # Retorna o número original se não conseguir formatar

async def solve_captcha(session):
    """Resolve o captcha de forma assíncrona"""
    # Enviar solicitação para resolver o captcha
    request_url = f"https://2captcha.com/in.php?key={API_KEY}&method=userrecaptcha&googlekey={SITE_KEY}&pageurl={PAGE_URL}&json=1"

    try:
        async with session.get(request_url) as response:
            if response.status != 200:
                return None

            result = await response.json()
            if result["status"] != 1:
                return None

            request_id = result["request"]

            # Aguardar a resolução do captcha
            for _ in range(30):  # Tentar por 30 vezes (5 minutos no máximo)
                await asyncio.sleep(10)  # Aguardar 10 segundos entre as verificações

                check_url = f"https://2captcha.com/res.php?key={API_KEY}&action=get&id={request_id}&json=1"
                async with session.get(check_url) as check_response:
                    if check_response.status != 200:
                        continue

                    check_result = await check_response.json()
                    if check_result["status"] == 1:
                        return check_result["request"]

                    if check_result["request"] != "CAPCHA_NOT_READY":
                        return None

            return None
    except Exception:
        return None

async def consultar_numero(session, telefone, captcha_response):
    """Consulta o número de forma assíncrona"""
    # Cookies da sessão
    cookies = {
        'JSESSIONID': '3458B9E282980BAD5853B4764FE7CDF2',
        'TS01c746c9': '01446233004889fe7f1d01127b2d4e26f74cb5a2d0aad2ccf20e77b6973fb3ab16cd7f6c52d76f954e78d5c77146afcba92ecf52d2420155a00f8b031634a79ff8f87b6ba0',
        'TS01e2203c': '01446233005e718239f04a0bf7560d661e14687300aad2ccf20e77b6973fb3ab16cd7f6c5255d7106717dee2fd9b25bba3d438c371',
        'TS4149caa5027': '08e9b45e80ab20002dcb7502293b34ab805e2f485e15a87cba876759e4022105d934b7cb7826128e087f3ed7b3113000a8431f607a2499f9187f89375cff27a80650c39d641a2e671cd0e1fff82b9ccb323e5dfdeb1d5f7bf409c31ef1b24a0d',
        '_ga_B580TMFC8B': 'GS1.1.1745788030.1.1.1745788261.0.0.0',
        '_ga': 'GA1.1.1146418538.1745788030',
        'TS00000000076': '08e9b45e80ab2800b2c8c0f61e9903e987de8b6a1b8289ce0b5f6248e10751a37d9eadc7d5ed896f55853464c7d3560d0819a9ce0209d000b9f4136fdab590d4de2aad4a22b3ade987d1c769fed881dc89190ac799736ee6d13c7f794fbb52edfd570faf53e42358f9123d3909f5f87216ce2300b1be633ed7de6ae8eda076e78eca7bb8acb5aa134e27bf2144ce510e6cbc59ce60606a24c32deb6f23ac4cdcdccbc2d3640ea460088d3967ca9e2881e08f49c42ce4f9a91799eca69c8ad7a11e8fbc6f9a9763d0f1317569aada532cd58200c6380e4006c4400e94aa8a6096700615b66da345171c5268c0a75548c08fee1a7490aa0d54ec5127d15648cdce507209b38b496aba',
        'TSPD_101_DID': '08e9b45e80ab2800b2c8c0f61e9903e987de8b6a1b8289ce0b5f6248e10751a37d9eadc7d5ed896f55853464c7d3560d0819a9ce0206380011a47b137c7c4f3ca0113be4323406a525b8a32da5f81e5ba051e1f1cd59b50dba4a78dd65ed4920009818e2f2c6b863084317d6f616a846',
    }

    # Headers da requisição
    headers = {
        'Host': 'consultanumero.abrtelecom.com.br',
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://consultanumero.abrtelecom.com.br/consultanumero/consulta/consultaSituacaoAtualCtg',
        'Origin': 'https://consultanumero.abrtelecom.com.br',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Priority': 'u=0, i',
        'Pragma': 'no-cache',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
    }

    # Dados do formulário
    data = {
        'telefone': telefone,
        'g-recaptcha-response': captcha_response,
    }

    try:
        # Enviar a requisição
        async with session.post(
            'https://consultanumero.abrtelecom.com.br/consultanumero/consulta/executaConsultaSituacaoAtual',
            cookies=cookies,
            headers=headers,
            data=data
        ) as response:
            return await response.text()
    except Exception:
        return None

def extrair_informacoes(telefone, html_content):
    """Extrai as informações relevantes do HTML retornado"""
    if not html_content:
        return None, "Falha na requisição HTTP"

    try:
        # Usar BeautifulSoup para analisar o HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # Verificar se há mensagem de erro no HTML
        mensagem_erro = soup.find('div', class_='alert-danger')
        if mensagem_erro:
            erro_texto = mensagem_erro.text.strip()
            return None, f"Erro retornado pelo site: {erro_texto}"

        # Verificar se há mensagem de número não encontrado
        if "não foi encontrado" in html_content or "não localizado" in html_content:
            return None, f"O número {telefone} não foi encontrado na base de dados."

        # Encontrar a tabela de resultados
        tabela = soup.find('table', id='resultado')

        if not tabela:
            # Tentar encontrar qualquer tabela que possa conter os resultados
            tabelas = soup.find_all('table')
            if tabelas:
                for t in tabelas:
                    if "prestadora" in t.text.lower() or "operadora" in t.text.lower():
                        tabela = t
                        break

            if not tabela:
                return None, "Não foi possível encontrar a tabela de resultados."

        # Extrair os cabeçalhos
        cabecalhos = []
        thead = tabela.find('thead')
        if thead:
            cabecalhos = [th.text.strip() for th in thead.find_all('th')]

        # Se não encontrou cabeçalhos no thead, procurar na primeira linha
        if not cabecalhos:
            primeira_linha = tabela.find('tr')
            if primeira_linha:
                cabecalhos = [th.text.strip() for th in primeira_linha.find_all('th')]

        # Extrair os dados da linha
        dados = []
        tbody = tabela.find('tbody')
        if tbody:
            primeira_linha_dados = tbody.find('tr')
            if primeira_linha_dados:
                dados = [td.text.strip() for td in primeira_linha_dados.find_all('td')]

        # Se não encontrou dados no tbody, procurar na segunda linha
        if not dados:
            linhas = tabela.find_all('tr')
            if len(linhas) > 1:
                dados = [td.text.strip() for td in linhas[1].find_all('td')]

        if not cabecalhos or not dados:
            return None, "Não foi possível extrair os dados da tabela."

        if len(dados) < 3:
            return None, f"Dados incompletos na resposta. Encontrados apenas {len(dados)} campos."

        # Extrair informações específicas
        data = dados[0]
        operadora = dados[1]
        razao_social = dados[2] if len(dados) > 2 else operadora

        # Limpar os dados
        data = data.strip()
        operadora = operadora.strip()
        razao_social = razao_social.strip()

        # Criar linha formatada para salvar no arquivo
        linha_formatada = f"CONSULTA DO NÚMERO: {telefone} - Data: {data} - Nome da Prestadora: {operadora} - Razão Social: {razao_social}"

        # Criar nome do arquivo baseado na operadora (sem espaços e caracteres especiais)
        nome_arquivo = re.sub(r'[^a-zA-Z0-9]', '', razao_social) + ".txt"

        # Criar chave única para evitar duplicatas
        chave_unica = f"{telefone}_{operadora}_{razao_social}"

        return {
            "operadora": operadora,
            "razao_social": razao_social,
            "nome_arquivo": nome_arquivo,
            "linha": linha_formatada,
            "chave_unica": chave_unica
        }, None

    except Exception as e:
        return None, f"Erro ao extrair informações: {str(e)}"

async def salvar_resultado(info):
    """Salva o resultado no arquivo da operadora de forma assíncrona"""
    try:
        # Verificar se já processamos este resultado (evitar duplicatas)
        if info["chave_unica"] in RESULTADOS_PROCESSADOS:
            return True

        # Adicionar ao conjunto de resultados processados
        RESULTADOS_PROCESSADOS.add(info["chave_unica"])

        # Criar diretório de resultados se não existir
        if not os.path.exists(DIRETORIO_RESULTADOS):
            os.makedirs(DIRETORIO_RESULTADOS)

        # Caminho completo do arquivo
        caminho_arquivo = os.path.join(DIRETORIO_RESULTADOS, info["nome_arquivo"])

        # Abrir arquivo em modo append
        async with aiofiles.open(caminho_arquivo, "a", encoding="utf-8") as f:
            await f.write(info["linha"] + "\n")

        return True

    except Exception:
        return False

async def salvar_html_debug(numero, html_content):
    """Salva o HTML para debug de forma assíncrona"""
    try:
        debug_dir = "debug_html"
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)

        # Remover caracteres não numéricos do número
        numero_limpo = re.sub(r'[^0-9]', '', numero)

        # Adicionar timestamp para evitar sobrescrever arquivos
        timestamp = datetime.now().strftime("%H%M%S")

        async with aiofiles.open(f"{debug_dir}/{numero_limpo}_{timestamp}.html", "w", encoding="utf-8") as f:
            await f.write(html_content)
    except Exception:
        pass  # Ignorar erros ao salvar o HTML de debug

async def processar_numero(session, numero, semaphore, total_numeros, index, progresso):
    """Processa um número de telefone de forma assíncrona"""
    async with semaphore:
        # Formatar o número
        numero_formatado = formatar_numero(numero)

        # Resolver captcha
        captcha_response = await solve_captcha(session)

        if not captcha_response:
            # Incrementar contador e exibir mensagem
            progresso["atual"] += 1
            print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: Falha ao resolver captcha")
            return False

        # Consultar o número
        html_result = await consultar_numero(session, numero_formatado, captcha_response)

        # Salvar o HTML para debug
        await salvar_html_debug(numero, html_result if html_result else "")

        # Extrair informações
        info, erro = extrair_informacoes(numero_formatado, html_result)

        if erro:
            # Verificar se a resposta contém mensagem de "número não encontrado"
            if "não encontrado" in erro.lower() or "não localizado" in erro.lower():
                # Incrementar contador e exibir mensagem
                progresso["atual"] += 1
                print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: Número não encontrado")
                return False

            # Se o erro for relacionado ao captcha ou tabela, tentar novamente
            if "captcha" in erro.lower() or "tabela" in erro.lower():
                await asyncio.sleep(2)  # Pequena pausa antes de tentar novamente

                # Tentar novamente com um novo captcha
                captcha_response = await solve_captcha(session)

                if captcha_response:
                    html_result = await consultar_numero(session, numero_formatado, captcha_response)
                    info, erro = extrair_informacoes(numero_formatado, html_result)

                    if erro:
                        # Incrementar contador e exibir mensagem
                        progresso["atual"] += 1
                        print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: {erro}")
                        return False
                else:
                    # Incrementar contador e exibir mensagem
                    progresso["atual"] += 1
                    print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: Falha ao resolver captcha na segunda tentativa")
                    return False
            else:
                # Incrementar contador e exibir mensagem
                progresso["atual"] += 1
                print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: {erro}")
                return False

        # Salvar o resultado
        if info:
            if await salvar_resultado(info):
                # Incrementar contadores e exibir mensagem
                progresso["atual"] += 1
                progresso["sucesso"] += 1
                print(f"[{progresso['atual']}/{total_numeros}] ✅ {numero_formatado}: {info['operadora']} ({info['razao_social']})")
                return True
            else:
                # Incrementar contador e exibir mensagem
                progresso["atual"] += 1
                print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: Falha ao salvar resultado")
                return False

        # Incrementar contador e exibir mensagem
        progresso["atual"] += 1
        print(f"[{progresso['atual']}/{total_numeros}] ❌ {numero_formatado}: Falha ao extrair informações")
        return False

async def ler_numeros():
    """Lê a lista de números do arquivo de forma assíncrona"""
    try:
        if not os.path.exists(ARQUIVO_NUMEROS):
            print(f"Arquivo {ARQUIVO_NUMEROS} não encontrado.")
            return []

        async with aiofiles.open(ARQUIVO_NUMEROS, "r", encoding="utf-8") as f:
            conteudo = await f.read()
            numeros = [linha.strip() for linha in conteudo.splitlines() if linha.strip()]

        # Remover duplicatas
        numeros = list(dict.fromkeys(numeros))

        return numeros

    except Exception as e:
        print(f"Erro ao ler arquivo de números: {str(e)}")
        return []

async def main():
    # Criar diretório de resultados se não existir
    if not os.path.exists(DIRETORIO_RESULTADOS):
        os.makedirs(DIRETORIO_RESULTADOS)

    # Registrar hora de início
    hora_inicio = time.time()

    # Ler a lista de números
    numeros = await ler_numeros()

    if not numeros:
        print(f"Nenhum número encontrado no arquivo {ARQUIVO_NUMEROS}.")
        return

    print(f"Encontrados {len(numeros)} números para consulta.")
    print(f"Processando com até {MAX_CONCURRENT_TASKS} consultas simultâneas...")

    # Semáforo para limitar o número de tarefas concorrentes
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_TASKS)

    # Dicionário para controlar o progresso
    progresso = {
        "atual": 0,
        "sucesso": 0
    }

    # Iniciar sessão HTTP
    async with aiohttp.ClientSession() as session:
        # Criar tarefas para processar cada número
        tarefas = [
            processar_numero(session, numero, semaphore, len(numeros), i, progresso)
            for i, numero in enumerate(numeros)
        ]

        # Executar todas as tarefas e aguardar a conclusão
        await asyncio.gather(*tarefas)

    # Calcular tempo total
    tempo_total = time.time() - hora_inicio
    minutos = int(tempo_total // 60)
    segundos = int(tempo_total % 60)

    print(f"\nProcessamento concluído em {minutos}m {segundos}s.")
    print(f"Consultas realizadas com sucesso: {progresso['sucesso']}/{len(numeros)} ({progresso['sucesso']/len(numeros)*100:.1f}%)")
    print(f"Os resultados foram salvos no diretório '{DIRETORIO_RESULTADOS}'.")

if __name__ == "__main__":
    # Instalar pacotes necessários se não estiverem instalados
    try:
        import aiohttp
        import aiofiles
    except ImportError:
        print("Instalando dependências necessárias...")
        import subprocess
        subprocess.call(["pip", "install", "aiohttp", "aiofiles"])
        print("Dependências instaladas. Reiniciando script...")
        import aiohttp
        import aiofiles

    # Executar o loop de eventos assíncrono
    asyncio.run(main())
