import threading
import requests
import subprocess
import json
import time
from typing import List, Dict, Tu<PERSON>
from concurrent.futures import ThreadPoolExecutor
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configurações
MAX_RETRIES = 8
TIMEOUT = 5
RETRY_DELAY = 10
INTERMEDIATE_CHECK_INTERVAL = 5  # Nova verificação a cada 5s para status intermediário

def get_all_apps() -> List[Dict]:
    """Obtém todos os apps Fly.io independente do status"""
    try:
        cmd = "flyctl apps list --json"
        result = subprocess.run(cmd.split(), capture_output=True, text=True, check=True)
        return json.loads(result.stdout)
    except Exception as e:
        print(f"⛔ Erro ao listar apps: {str(e)}")
        return []

def renew_ip(app_name: str):
    """Gerencia a renovação de IPs com tratamento de erros corrigido"""
    try:
        # Listar IPs existentes
        list_cmd = f"flyctl ips list -a {app_name} --json"
        result = subprocess.run(list_cmd.split(), capture_output=True, text=True, check=True)
        
        ips = []
        try:
            ips_data = json.loads(result.stdout)
            ips = [
                ip["Address"] for ip in ips_data 
                if isinstance(ip, dict) and 
                ip.get("Type", "").lower() == "v4" and 
                "Address" in ip
            ]
        except json.JSONDecodeError:
            print(f"⚠️ Resposta inválida de IPs: {result.stdout}")

        # Liberar IPs existentes
        for ip in ips:
            try:
                release_cmd = f"flyctl ips release -a {app_name} {ip}"
                subprocess.run(release_cmd.split(), check=True, capture_output=True)
                print(f"✅ IP {ip} liberado de {app_name}")
            except subprocess.CalledProcessError as e:
                print(f"⚠️ Erro liberando IP de {app_name}: {e.stderr}")

        # Alocar novo IP
        allocate_cmd = f"flyctl ips allocate-v4 -a {app_name}"
        result = subprocess.run(
            allocate_cmd.split(),
            capture_output=True,
            text=True,
            timeout=30,
            check=True
        )
        
        # Parse do novo IP
        new_ip = next(line.split()[-1] for line in result.stdout.split('\n') if 'v4' in line)
        print(f"🔄 Novo IP {new_ip} alocado para {app_name}")
        return new_ip

    except Exception as e:
        print(f"⛔ Erro crítico em {app_name}: {str(e)}")
        return None

def check_status_continuously(session: requests.Session, app_name: str) -> Dict:
    """Verificação contínua até status final com intervalo de 5 segundos"""
    url = f"https://{app_name}.fly.dev/postfix-status"
    
    while True:
        try:
            resp = session.get(url, timeout=TIMEOUT)
            if resp.status_code == 200:
                data = resp.json()
                status = data.get("emailStatus", "NO_STATUS_FIELD")
                
                if status.lower() in ("entregue", "falha - email retornou"):
                    print(f"[{app_name}] ✅ Status final: {status}")
                    if status.lower() == "entregue":
                        save_delivered_app(app_name)
                    return {
                        'status': status,
                        'healthy': status.lower() == "entregue"
                    }
                
                print(f"[{app_name}] 🔄 Status intermediário: {status}")
                time.sleep(INTERMEDIATE_CHECK_INTERVAL)
                
            else:
                print(f"[{app_name}] ⚠️ HTTP {resp.status_code}")
                return {
                    'status': f"HTTP {resp.status_code}",
                    'healthy': False
                }
                
        except Exception as e:
            print(f"[{app_name}] ⚠️ Erro: {str(e)}")
            return {
                'status': str(e),
                'healthy': False
            }

def fetch_email_status(session: requests.Session, app_name: str) -> Tuple[str, bool, Dict]:
    """Verificação principal com retentativas"""
    for attempt in range(MAX_RETRIES):
        print(f"[{app_name}] 🔍 Tentativa {attempt + 1} de {MAX_RETRIES}")
        result = check_status_continuously(session, app_name)
        
        if result['status'].lower() in ("entregue", "falha - email retornou"):
            if result['status'].lower() == "falha - email retornou":
                # Renovar IP imediatamente para apps com falha
                renew_ip(app_name)
            return app_name, result['status'].lower() == "falha - email retornou", result
            
        time.sleep(3 ** attempt)  # Backoff exponencial
    
    # Se chegou aqui é porque não obteve status final
    print(f"[{app_name}] ⛔ Status indefinido após {MAX_RETRIES} tentativas")
    return app_name, True, {'status': 'Status indefinido', 'healthy': False}

def list_ips(app_name: str):
    """Lista IPs com tratamento robusto de erros"""
    try:
        cmd = f"flyctl ips list -a {app_name} --json"
        result = subprocess.run(cmd.split(), capture_output=True, text=True, check=True)
        
        print(f"\n🌐 IPs para {app_name}:")
        
        try:
            ips = json.loads(result.stdout)
            
            if not ips:
                print(" - Nenhum IP alocado")
                return
                
            for ip in ips:
                ip_type = ip.get('Type', ip.get('type', 'desconhecido')).lower()
                address = ip.get('Address', ip.get('address', 'indisponível'))
                region = ip.get('Region', ip.get('region', 'região não especificada'))
                
                ip_type = ip_type.replace('_', ' ').replace('v4', 'IPv4').replace('v6', 'IPv6')
                region = region.split(' (')[0]
                
                print(f" - {ip_type.upper()}: {address} ({region})")
                
        except json.JSONDecodeError:
            print(f"⛔ Resposta inválida: {result.stdout}")
            
    except subprocess.CalledProcessError as e:
        print(f"⛔ Erro ao executar flyctl: {e.stderr}")
    except Exception as e:
        print(f"⛔ Erro inesperado: {str(e)}")

def check_app(session: requests.Session, app: Dict):
    """Verificação individual com tratamento de erros"""
    try:
        result = fetch_email_status(session, app['Name'])
        list_ips(app['Name'])
        return result
    except Exception as e:
        print(f"⚠️ Erro não tratado em {app['Name']}: {str(e)}")
        return app['Name'], True, {'status': str(e), 'healthy': False}

def handle_failed_apps(failed_apps: List[str]):
    """Processo de renovação de IPs para apps com falha"""
    print(f"\n🚨 Iniciando renovação de IPs para: {failed_apps}")
    
    for app in failed_apps:
        print(f"\n🔄 Processando app {app}:")
        # 1. Listar IPs atuais
        list_cmd = f"flyctl ips list -a {app} --json"
        result = subprocess.run(list_cmd.split(), capture_output=True, text=True)
        
        try:
            ips_data = json.loads(result.stdout)
            # 2. Remover todos IPs existentes
            for ip in ips_data:
                if ip.get("Type", "").lower() == "v4":
                    ip_addr = ip.get("Address", "")
                    if ip_addr:
                        release_cmd = f"flyctl ips release -a {app} {ip_addr}"
                        subprocess.run(release_cmd.split(), check=True)
                        print(f"  ✅ IP {ip_addr} removido")
            
            # 3. Alocar novo IP
            allocate_cmd = f"flyctl ips allocate-v4 -a {app}"
            result = subprocess.run(allocate_cmd.split(), capture_output=True, text=True)
            if result.returncode == 0:
                new_ip = next(line.split()[-1] for line in result.stdout.split('\n') if 'v4' in line)
                print(f"  🔄 Novo IP {new_ip} alocado")
            else:
                print(f"  ⚠️ Falha ao alocar novo IP: {result.stderr}")
        
        except Exception as e:
            print(f"  ⚠️ Erro processando app {app}: {str(e)}")
    
    print(f"\n🕒 Aguardando {RETRY_DELAY} segundos antes de revalidar...")
    time.sleep(RETRY_DELAY)

def load_delivered_apps() -> set:
    """Carrega apps já entregues do arquivo"""
    try:
        with open('entregues.txt', 'r') as f:
            return set(line.strip() for line in f if line.strip())
    except FileNotFoundError:
        return set()

def save_delivered_app(app_name: str):
    """Salva apenas o nome do app entregue no arquivo"""
    try:
        with open('entregues.txt', 'a+') as f:
            f.write(f"{app_name}\n")
    except Exception as e:
        print(f"⚠️ Erro ao salvar app entregue: {e}")

def monitor_and_renew():
    """Monitoramento contínuo com tratamento de status intermediário"""
    session = requests.Session()
    session.verify = False  # Desativa verificação SSL
    delivered_apps = load_delivered_apps()
    
    while True:
        print("\n" + "="*50 + "\nIniciando ciclo de monitoramento\n" + "="*50)
        
        apps = [app for app in get_all_apps() if app['Name'] not in delivered_apps]
        failed_apps = []
        
        if not apps:
            print("✅ Todos os apps já foram entregues")
            time.sleep(300)
            continue
            
        with ThreadPoolExecutor() as executor:
            futures = [executor.submit(check_app, session, app) for app in apps]
            results = [f.result() for f in futures]
        
        for result in results:
            if isinstance(result, Exception):
                print(f"⚠️ Erro global: {str(result)}")
                continue
            
            app_name, needs_renewal, status_data = result
            if needs_renewal or not status_data['healthy']:
                failed_apps.append(app_name)
        
        if failed_apps:
            handle_failed_apps(failed_apps)
            
            print("\n🔍 Revalidando apps após renovação...")
            with ThreadPoolExecutor() as executor:
                retry_futures = [executor.submit(fetch_email_status, session, app) for app in failed_apps]
                retry_results = [f.result() for f in retry_futures]
            
            still_failing = [app for app, failed, _ in retry_results if failed]
            if still_failing:
                print(f"\n⛔ Apps ainda com problemas: {still_failing}")
            else:
                print("\n✅ Todos os apps recuperados!")
        
        print("🕒 Próxima verificação em 5 minutos...")
        time.sleep(300)

if __name__ == "__main__":
    try:
        monitor_and_renew()
    except KeyboardInterrupt:
        print("\n🛑 Monitoramento interrompido pelo usuário")
    except Exception as e:
        print(f"\n⛔ Erro fatal: {str(e)}")
