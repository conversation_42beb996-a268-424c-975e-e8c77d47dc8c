import aiohttp
import asyncio
import csv
from datetime import datetime

import json
import re

def load_tokens():
    tokens = []
    with open('tokens', 'r') as f:
        for line in f:
            if line.strip():
                match = re.match(r'TOKEN: (.+?) IDS: (\[.+\])', line)
                if match:
                    token = match.group(1)
                    ids = json.loads(match.group(2).replace("'", '"'))
                    tokens.append({
                        'token': token,
                        'ids': ids,
                        'usage': {id: 0 for id in ids}
                    })
    return tokens

tokens = load_tokens()
current_token_index = 0
current_patient_index = 0

def get_next_patient_url():
    global current_token_index, current_patient_index
    
    token_data = tokens[current_token_index]
    patient_id = token_data['ids'][current_patient_index]
    
    # Update usage count
    token_data['usage'][patient_id] += 1
    
    # Check if we need to rotate token (100 uses per ID)
    if token_data['usage'][patient_id] >= 100:
        current_token_index = (current_token_index + 1) % len(tokens)
        token_data = tokens[current_token_index]
        current_patient_index = 0
        patient_id = token_data['ids'][current_patient_index]
    else:
        current_patient_index = (current_patient_index + 1) % len(token_data['ids'])
    
    # Update headers with current token
    HEADERS['authorization'] = f"Bearer {token_data['token']}"
    
    return f"https://app.clinicaexperts.com.br/api/person/patient/{patient_id}"

HEADERS = {
    "Host": "app.clinicaexperts.com.br",
    "Cookie": "_ga=GA1.1.**********.**********; _tt_enable_cookie=1; _ttp=01JRYKR4N633WVR7HDYGVPB9RW_.tt.2; _ga_CN74PL5BB5=GS1.1.**********.1.1.**********.58.0.193327098; _gcl_au=1.1.**********.**********.93942806.**********.**********; _ga_PZZRRKJYL8=GS1.1.**********.1.1.**********.0.0.0; _ga_H3YQJB02XK=GS1.1.**********.1.1.**********.0.0.0",
    "sec-ch-ua-platform": "\"macOS\"",
    "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"",
    "sec-ch-ua-mobile": "?0",
    "clinicaexperts-app-version": "3.22.2",
    "x-requested-with": "XMLHttpRequest",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "accept": "application/json, text/plain, */*",
    "content-type": "application/json;charset=UTF-8",
    "origin": "https://app.clinicaexperts.com.br",
    "sec-fetch-site": "same-origin",
    "sec-fetch-mode": "cors",
    "sec-fetch-dest": "empty",
    "referer": lambda: f"https://app.clinicaexperts.com.br/clinica/contatos/listagem/paciente/{tokens[current_token_index]['ids'][current_patient_index]}/informacoes?person_modal_mode=edit&person_modal_type=patient&person_modal_entity_id={tokens[current_token_index]['ids'][current_patient_index]}",
    "accept-language": "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7",
    "priority": "u=1, i",
    "pragma": "no-cache",
    "cache-control": "no-cache",
    "authorization": "Bearer 966213|HBgODPTs5UQoTj15HILTg1oAcADuwqgeT4ZuKdRq"
}

async def update_patient(session, name, email):
    payload = {
        "name": name,
        "active": True,
        "file": None,
        "phone": {
            "id": 3241195,
            "type": "mobile",
            "phone_type": "mobile",
            "category": "billing",
            "number": "+5511937611730",
            "national_number": "+55 (11) 93761-1730",
            "country": "BR"
        },
        "email": email,
        "contacts": [{
            "id": 3241196,
            "type": "mobile",
            "phone_type": "mobile",
            "category": "home",
            "number": "+5511937611730",
            "national_number": "+55 (11) 93761-1730",
            "country": "BR"
        }],
        "documents": [{
            "type": None,
            "value": None
        }],
        "address": {
            "country": {
                "id": None,
                "code": "BR",
                "name": None
            },
            "state_id": None,
            "city_id": None,
            "zip_code": None,
            "neighborhood": None,
            "street": None,
            "number": None,
            "complement": None
        },
        "sex": None,
        "date_birth": None,
        "marital_status": None,
        "occupation": None,
        "attachments": [],
        "cpf": "000.000.001-91",
        "rg": None,
        "origin": None,
        "referral_person_id": None,
        "tags_uuids": [],
        "notifications": [{
            "type": "SMS",
            "value": True
        }, {
            "type": "EMAIL",
            "value": True
        }, {
            "type": "WHATSAPP",
            "value": True
        }],
        "person_healthcare_companies": [],
        "emergency_contacts": [],
        "responsible_contact": None,
        "first_name": name.split()[0],
        "annotation": None,
        "document": "000.000.001-91",
        "notification_channel_sms": True,
        "notification_channel_email": True,
        "notification_channel_whatsapp": True
    }

    try:
        # Create headers copy and resolve any lambda values
        request_headers = HEADERS.copy()
        if callable(request_headers['referer']):
            request_headers['referer'] = request_headers['referer']()
            
        async with session.put(get_next_patient_url(), json=payload, headers=request_headers) as response:
            if response.status == 200:
                print(f"Sucesso ao atualizar {name} ({email}) {tokens[current_token_index]['token']}")
                return True
            else:
                error = await response.text()
                print(f"Erro ao atualizar {name} ({email}): {response.status} - {error}")
                return False
    except Exception as e:
        print(f"Erro na requisição para {name} ({email}): {str(e)}")
        return False

async def process_file(filename):
    successes = 0
    failures = 0
    
    async with aiohttp.ClientSession() as session:
        with open(filename, 'r') as file:
            reader = csv.reader(file)
            for row in reader:
                if len(row) >= 2:
                    name = row[0].strip()
                    email = row[1].strip()
                    if await update_patient(session, name, email):
                        successes += 1
                    else:
                        failures += 1
                else:
                    print(f"Linha inválida: {row}")
                    
    print(f"\nResumo:")
    print(f"Atualizações bem-sucedidas: {successes}")
    print(f"Falhas: {failures}")

if __name__ == "__main__":
    print(f"Iniciando atualização em {datetime.now()}")
    asyncio.run(process_file("emails.txt"))
    print(f"Concluído em {datetime.now()}")