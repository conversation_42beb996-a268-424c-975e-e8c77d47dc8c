API_TOKEN = '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************HM6Ly9hcGkuZmx5LmlvL2FhYS92MZgEks5n98TyzwAAAAEj7+MQF84ADsOBCpHOAA7DgQzEEGJzXR+dBk/fbFBIq+TE/L/EIICnCjSaqf4JwyM8XnUEl0L5XBZdu51cFF7/92ZFB7VC'

import requests
import time
from typing import List, Dict, Tuple, Set
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import datetime

# Configurações
API_BASE_URL = "https://api.fly.io/v1"

HEADERS = {
    "Authorization": f"Bearer {API_TOKEN}",
    "Content-Type": "application/json"
}
TIMEOUT = 10
RETRY_DELAY = 10
INTERMEDIATE_CHECK_INTERVAL = 5
MAX_RETRIES = 8
MONITOR_INTERVAL = 300  # 5 minutos entre ciclos completos

def get_all_apps() -> List[Dict]:
    """Obtém todos os apps Fly.io independente do status"""
    try:
        url = f"{API_BASE_URL}/apps"
        response = requests.get(url, headers=HEADERS, timeout=TIMEOUT)
        response.raise_for_status()
        
        # A resposta da API retorna um dicionário com a chave "apps"
        apps = response.json()
        if isinstance(apps, dict) and "apps" in apps:
            return apps["apps"]
        else:
            print("Formato de resposta inesperado da API:", apps)
            return []
    except Exception as e:
        print(f"⛔ Erro ao listar apps: {str(e)}")
        return []

def get_app_details(app_name: str) -> Dict:
    """Obtém detalhes de um app específico"""
    try:
        url = f"{API_BASE_URL}/apps/{app_name}"
        response = requests.get(url, headers=HEADERS, timeout=TIMEOUT)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            print(f"App {app_name} não encontrado.")
            return None
        else:
            print(f"Erro ao obter detalhes do app {app_name}: {e}")
            return None
    except Exception as e:
        print(f"Erro ao obter detalhes do app {app_name}: {e}")
        return None

def delete_app(app_name: str, force: bool = True) -> bool:
    """Exclui um aplicativo específico"""
    try:
        url = f"{API_BASE_URL}/apps/{app_name}?force={str(force).lower()}"
        response = requests.delete(url, headers=HEADERS, timeout=TIMEOUT)
        response.raise_for_status()
        print(f"🗑️ App {app_name} excluído com sucesso")
        return True
    except requests.exceptions.HTTPError as e:
        print(f"⚠️ Erro ao excluir app {app_name}: {e}")
        return False
    except Exception as e:
        print(f"⚠️ Erro ao excluir app {app_name}: {e}")
        return False

def check_postfix_status(session: requests.Session, app_name: str) -> Dict:
    """Verifica o status do postfix até obter um status final"""
    url = f"https://{app_name}.fly.dev/postfix-status"
    start_time = time.time()
    
    while True:
        try:
            resp = session.get(url, timeout=TIMEOUT, verify=False)
            if resp.status_code == 200:
                data = resp.json()
                status = data.get("emailStatus", "NO_STATUS_FIELD").lower()
                
                if status in ("entregue", "falha - email retornou"):
                    print(f"[{app_name}] ✅ Status final obtido após {int(time.time() - start_time)}s: {status}")
                    return {
                        'status': status,
                        'healthy': status == "entregue",
                        'details': data
                    }
                
                print(f"[{app_name}] 🔄 Status intermediário: {status}")
                time.sleep(INTERMEDIATE_CHECK_INTERVAL)
                
            else:
                print(f"[{app_name}] ⚠️ HTTP {resp.status_code}")
                return {
                    'status': f"HTTP {resp.status_code}",
                    'healthy': False,
                    'details': {}
                }
                
        except Exception as e:
            print(f"[{app_name}] ⚠️ Erro: {str(e)}")
            return {
                'status': str(e),
                'healthy': False,
                'details': {}
            }

def fetch_final_status(session: requests.Session, app_name: str) -> Tuple[str, bool, Dict]:
    """Obtém o status final com retentativas"""
    for attempt in range(MAX_RETRIES):
        print(f"[{app_name}] 🔍 Tentativa {attempt + 1} de {MAX_RETRIES}")
        result = check_postfix_status(session, app_name)
        
        if result['status'].lower() in ("entregue", "falha - email retornou"):
            return app_name, result['status'].lower() == "falha - email retornou", result
            
        time.sleep(RETRY_DELAY)
    
    print(f"[{app_name}] ⛔ Status indefinido após {MAX_RETRIES} tentativas")
    return app_name, True, {'status': 'Status indefinido', 'healthy': False, 'details': {}}

def process_app(session: requests.Session, app_name: str, delivered_apps: Set[str]) -> bool:
    """Processa um app individualmente"""
    if app_name in delivered_apps:
        print(f"[{app_name}] ✅ Já foi entregue anteriormente")
        return True
    
    print(f"\n{'='*50}\nProcessando app: {app_name}\n{'='*50}")
    
    # Verificar status atual
    app_name, needs_renewal, status_data = fetch_final_status(session, app_name)
    
    # Se entregue, salvar e retornar
    if status_data.get('healthy', False):
        save_delivered_app(app_name)
        return True
    
    # Se falha ou indefinido após todas as tentativas
    if needs_renewal or status_data['status'] == 'Status indefinido':
        print(f"[{app_name}] 🚨 Falha detectada ou status indefinido - deletando aplicativo")
        deleted = delete_app(app_name)
        if deleted:
            log_status(app_name, "Deletado devido a falha ou status indefinido")
            return False
    
    return False

def load_delivered_apps() -> Set[str]:
    """Carrega apps já entregues do arquivo"""
    try:
        with open('entregues.txt', 'r') as f:
            return set(line.strip() for line in f if line.strip())
    except FileNotFoundError:
        return set()

def save_delivered_app(app_name: str):
    """Salva o nome do app entregue no arquivo"""
    try:
        with open('entregues.txt', 'a') as f:
            f.write(f"{app_name}\n")
        print(f"[{app_name}] 📝 Salvo como entregue")
    except Exception as e:
        print(f"[{app_name}] ⚠️ Erro ao salvar app entregue: {e}")

def log_status(app_name: str, status: str):
    """Loga o status do app para histórico"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open('status.log', 'a') as f:
            f.write(f"{timestamp} - {app_name}: {status}\n")
    except Exception as e:
        print(f"⚠️ Erro ao registrar log: {e}")

def monitor_apps():
    """Monitoramento principal dos apps"""
    session = requests.Session()
    session.verify = False  # Desativa a verificação SSL
    delivered_apps = load_delivered_apps()
    
    while True:
        print(f"\n{'='*50}\nIniciando ciclo de monitoramento - {datetime.now()}\n{'='*50}")
        
        # Obter todos os apps não entregues
        all_apps = get_all_apps()
        if all_apps is None:
            print("Erro ao obter a lista de apps. Tentando novamente no próximo ciclo.")
            time.sleep(MONITOR_INTERVAL)
            continue
        
        apps = [app for app in all_apps if app['name'] not in delivered_apps]
        
        if not apps:
            print("✅ Todos os apps já foram entregues ou excluídos")
            time.sleep(MONITOR_INTERVAL)
            continue
            
        print(f"📋 Apps a verificar: {len(apps)}")
        
        # Processar cada app em paralelo
        with ThreadPoolExecutor() as executor:
            futures = []
            for app in apps:
                futures.append(executor.submit(
                    process_app, 
                    session, 
                    app['name'], 
                    delivered_apps
                ))
            
            # Aguardar resultados
            results = [f.result() for f in futures]
            delivered_apps = load_delivered_apps()  # Recarregar lista atualizada
        
        print(f"\nResumo do ciclo:")
        print(f"- Total de apps processados: {len(apps)}")
        print(f"- Apps entregues neste ciclo: {sum(results)}")
        print(f"- Apps com problemas ou excluídos: {len(apps) - sum(results)}")
        
        print(f"\n🕒 Próxima verificação em {MONITOR_INTERVAL//60} minutos...")
        time.sleep(MONITOR_INTERVAL)

if __name__ == "__main__":
    try:
        monitor_apps()
    except KeyboardInterrupt:
        print("\n🛑 Monitoramento interrompido pelo usuário")
    except Exception as e:
        print(f"\n⛔ Erro fatal: {str(e)}")
