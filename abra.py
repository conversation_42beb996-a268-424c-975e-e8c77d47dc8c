import requests
import json
import time
import re
import os
from bs4 import BeautifulSoup

# Configurações do 2captcha
API_KEY = "104e4275728d77f04d6c2bf11a93538d"  # Sua chave de API do 2captcha
SITE_KEY = "6LetGEImAAAAAIji8OYruian_LlJwIW9E3ZC-0ps"  # Site key do reCAPTCHA
PAGE_URL = "https://consultanumero.abrtelecom.com.br/consultanumero/consulta/consultaSituacaoAtualCtg"
ARQUIVO_NUMEROS = "numeros.txt"  # Arquivo com a lista de números
DIRETORIO_RESULTADOS = "resultados"  # Diretório para salvar os resultados

def formatar_numero(numero):
    """Formata o número para o padrão aceito pelo site (XX) XXXXX-XXXX"""
    # Remove todos os caracteres não numéricos
    numero_limpo = re.sub(r'\D', '', numero)

    # Verifica se o número tem o tamanho correto (11 dígitos para celular)
    if len(numero_limpo) == 11:
        return f"({numero_limpo[:2]}) {numero_limpo[2:7]}-{numero_limpo[7:]}"
    # 10 dígitos para telefone fixo
    elif len(numero_limpo) == 10:
        return f"({numero_limpo[:2]}) {numero_limpo[2:6]}-{numero_limpo[6:]}"
    else:
        return numero  # Retorna o número original se não conseguir formatar

def solve_captcha():
    print("Enviando solicitação para resolver o captcha...")

    # Enviar solicitação para resolver o captcha
    request_url = f"https://2captcha.com/in.php?key={API_KEY}&method=userrecaptcha&googlekey={SITE_KEY}&pageurl={PAGE_URL}&json=1"
    response = requests.get(request_url)

    if response.status_code != 200:
        print(f"Erro ao enviar solicitação: {response.status_code}")
        return None

    result = response.json()
    if result["status"] != 1:
        print(f"Erro: {result['request']}")
        return None

    request_id = result["request"]
    print(f"Solicitação enviada com sucesso. ID: {request_id}")

    # Aguardar a resolução do captcha
    for _ in range(30):  # Tentar por 30 vezes (5 minutos no máximo)
        time.sleep(10)  # Aguardar 10 segundos entre as verificações

        check_url = f"https://2captcha.com/res.php?key={API_KEY}&action=get&id={request_id}&json=1"
        response = requests.get(check_url)

        if response.status_code != 200:
            print(f"Erro ao verificar status: {response.status_code}")
            continue

        result = response.json()
        if result["status"] == 1:
            captcha_response = result["request"]
            print("Captcha resolvido com sucesso!")
            return captcha_response

        if result["request"] != "CAPCHA_NOT_READY":
            print(f"Erro: {result['request']}")
            return None

        print("Captcha ainda não está pronto, aguardando...")

    print("Tempo limite excedido para resolver o captcha")
    return None

def consultar_numero(telefone, captcha_response):
    print(f"Consultando número {telefone}...")

    # Cookies da sessão
    cookies = {
        'JSESSIONID': '3458B9E282980BAD5853B4764FE7CDF2',
        'TS01c746c9': '01446233004889fe7f1d01127b2d4e26f74cb5a2d0aad2ccf20e77b6973fb3ab16cd7f6c52d76f954e78d5c77146afcba92ecf52d2420155a00f8b031634a79ff8f87b6ba0',
        'TS01e2203c': '01446233005e718239f04a0bf7560d661e14687300aad2ccf20e77b6973fb3ab16cd7f6c5255d7106717dee2fd9b25bba3d438c371',
        'TS4149caa5027': '08e9b45e80ab20002dcb7502293b34ab805e2f485e15a87cba876759e4022105d934b7cb7826128e087f3ed7b3113000a8431f607a2499f9187f89375cff27a80650c39d641a2e671cd0e1fff82b9ccb323e5dfdeb1d5f7bf409c31ef1b24a0d',
        '_ga_B580TMFC8B': 'GS1.1.1745788030.1.1.1745788261.0.0.0',
        '_ga': 'GA1.1.1146418538.1745788030',
        'TS00000000076': '08e9b45e80ab2800b2c8c0f61e9903e987de8b6a1b8289ce0b5f6248e10751a37d9eadc7d5ed896f55853464c7d3560d0819a9ce0209d000b9f4136fdab590d4de2aad4a22b3ade987d1c769fed881dc89190ac799736ee6d13c7f794fbb52edfd570faf53e42358f9123d3909f5f87216ce2300b1be633ed7de6ae8eda076e78eca7bb8acb5aa134e27bf2144ce510e6cbc59ce60606a24c32deb6f23ac4cdcdccbc2d3640ea460088d3967ca9e2881e08f49c42ce4f9a91799eca69c8ad7a11e8fbc6f9a9763d0f1317569aada532cd58200c6380e4006c4400e94aa8a6096700615b66da345171c5268c0a75548c08fee1a7490aa0d54ec5127d15648cdce507209b38b496aba',
        'TSPD_101_DID': '08e9b45e80ab2800b2c8c0f61e9903e987de8b6a1b8289ce0b5f6248e10751a37d9eadc7d5ed896f55853464c7d3560d0819a9ce0206380011a47b137c7c4f3ca0113be4323406a525b8a32da5f81e5ba051e1f1cd59b50dba4a78dd65ed4920009818e2f2c6b863084317d6f616a846',
    }

    # Headers da requisição
    headers = {
        'Host': 'consultanumero.abrtelecom.com.br',
        'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Referer': 'https://consultanumero.abrtelecom.com.br/consultanumero/consulta/consultaSituacaoAtualCtg',
        'Origin': 'https://consultanumero.abrtelecom.com.br',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'same-origin',
        'Sec-Fetch-User': '?1',
        'Priority': 'u=0, i',
        'Pragma': 'no-cache',
        'Cache-Control': 'no-cache',
        'Content-Type': 'application/x-www-form-urlencoded',
    }

    # Dados do formulário
    data = {
        'telefone': telefone,
        'g-recaptcha-response': captcha_response,
    }

    # Enviar a requisição
    response = requests.post(
        'https://consultanumero.abrtelecom.com.br/consultanumero/consulta/executaConsultaSituacaoAtual',
        cookies=cookies,
        headers=headers,
        data=data,
    )

    return response.text

def extrair_informacoes(telefone, html_content):
    """Extrai as informações relevantes do HTML retornado"""
    try:
        # Usar BeautifulSoup para analisar o HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # Verificar se há mensagem de erro no HTML
        mensagem_erro = soup.find('div', class_='alert-danger')
        if mensagem_erro:
            erro_texto = mensagem_erro.text.strip()
            return None, f"Erro retornado pelo site: {erro_texto}"

        # Verificar se há mensagem de número não encontrado
        if "não foi encontrado" in html_content or "não localizado" in html_content:
            return None, f"O número {telefone} não foi encontrado na base de dados."

        # Encontrar a tabela de resultados
        tabela = soup.find('table', id='resultado')

        if not tabela:
            # Tentar encontrar qualquer tabela que possa conter os resultados
            tabelas = soup.find_all('table')
            if tabelas:
                for t in tabelas:
                    if "prestadora" in t.text.lower() or "operadora" in t.text.lower():
                        tabela = t
                        break

            if not tabela:
                return None, "Não foi possível encontrar a tabela de resultados."

        # Extrair os cabeçalhos
        cabecalhos = []
        thead = tabela.find('thead')
        if thead:
            cabecalhos = [th.text.strip() for th in thead.find_all('th')]

        # Se não encontrou cabeçalhos no thead, procurar na primeira linha
        if not cabecalhos:
            primeira_linha = tabela.find('tr')
            if primeira_linha:
                cabecalhos = [th.text.strip() for th in primeira_linha.find_all('th')]

        # Extrair os dados da linha
        dados = []
        tbody = tabela.find('tbody')
        if tbody:
            primeira_linha_dados = tbody.find('tr')
            if primeira_linha_dados:
                dados = [td.text.strip() for td in primeira_linha_dados.find_all('td')]

        # Se não encontrou dados no tbody, procurar na segunda linha
        if not dados:
            linhas = tabela.find_all('tr')
            if len(linhas) > 1:
                dados = [td.text.strip() for td in linhas[1].find_all('td')]

        if not cabecalhos or not dados:
            return None, "Não foi possível extrair os dados da tabela."

        if len(dados) < 3:
            return None, f"Dados incompletos na resposta. Encontrados apenas {len(dados)} campos."

        # Extrair informações específicas
        data = dados[0]
        operadora = dados[1]
        razao_social = dados[2] if len(dados) > 2 else operadora

        # Limpar os dados
        data = data.strip()
        operadora = operadora.strip()
        razao_social = razao_social.strip()

        # Criar linha formatada para salvar no arquivo
        linha_formatada = f"CONSULTA DO NÚMERO: {telefone} - Data: {data} - Nome da Prestadora: {operadora} - Razão Social: {razao_social}"

        # Criar nome do arquivo baseado na operadora (sem espaços e caracteres especiais)
        nome_arquivo = re.sub(r'[^a-zA-Z0-9]', '', razao_social) + ".txt"

        return {
            "operadora": operadora,
            "razao_social": razao_social,
            "nome_arquivo": nome_arquivo,
            "linha": linha_formatada
        }, None

    except Exception as e:
        return None, f"Erro ao extrair informações: {str(e)}"

def salvar_resultado(info):
    """Salva o resultado no arquivo da operadora"""
    try:
        # Criar diretório de resultados se não existir
        if not os.path.exists(DIRETORIO_RESULTADOS):
            os.makedirs(DIRETORIO_RESULTADOS)

        # Caminho completo do arquivo
        caminho_arquivo = os.path.join(DIRETORIO_RESULTADOS, info["nome_arquivo"])

        # Abrir arquivo em modo append
        with open(caminho_arquivo, "a", encoding="utf-8") as f:
            f.write(info["linha"] + "\n")

        print(f"Resultado salvo em {caminho_arquivo}")
        return True

    except Exception as e:
        print(f"Erro ao salvar resultado: {str(e)}")
        return False

def ler_numeros():
    """Lê a lista de números do arquivo"""
    try:
        if not os.path.exists(ARQUIVO_NUMEROS):
            print(f"Arquivo {ARQUIVO_NUMEROS} não encontrado.")
            return []

        with open(ARQUIVO_NUMEROS, "r", encoding="utf-8") as f:
            numeros = [linha.strip() for linha in f if linha.strip()]

        return numeros

    except Exception as e:
        print(f"Erro ao ler arquivo de números: {str(e)}")
        return []

def main():
    # Ler a lista de números
    numeros = ler_numeros()

    if not numeros:
        print(f"Nenhum número encontrado no arquivo {ARQUIVO_NUMEROS}.")
        return

    print(f"Encontrados {len(numeros)} números para consulta.")

    # Contador de consultas bem-sucedidas
    consultas_ok = 0

    # Processar cada número
    for i, numero in enumerate(numeros):
        # Formatar o número
        numero_formatado = formatar_numero(numero)

        print(f"\nProcessando {i+1}/{len(numeros)}: {numero_formatado}")

        # Resolver um novo captcha para cada número
        print("Obtendo novo captcha para esta consulta...")
        captcha_response = solve_captcha()

        if not captcha_response:
            print("Não foi possível resolver o captcha. Continuando com o próximo número...")
            continue

        # Consultar o número
        html_result = consultar_numero(numero_formatado, captcha_response)

        # Salvar o HTML para debug
        debug_dir = "debug_html"
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)

        # Remover caracteres não numéricos do número
        numero_limpo = re.sub(r'[^0-9]', '', numero)
        with open(f"{debug_dir}/{numero_limpo}.html", "w", encoding="utf-8") as f:
            f.write(html_result)

        # Extrair informações
        info, erro = extrair_informacoes(numero_formatado, html_result)

        if erro:
            print(f"Erro: {erro}")

            # Verificar se a resposta contém mensagem de "número não encontrado"
            if "não encontrado" in html_result.lower() or "não localizado" in html_result.lower():
                print(f"O número {numero_formatado} não foi encontrado na base de dados.")
                continue

            # Se o erro for relacionado ao captcha, tentar renovar
            if "captcha" in erro.lower() or "tabela" in erro.lower():
                print("Tentando renovar o captcha...")
                time.sleep(5)  # Esperar um pouco antes de tentar novamente
                captcha_response = solve_captcha()

                if captcha_response:
                    # Tentar novamente com o novo captcha
                    html_result = consultar_numero(numero_formatado, captcha_response)
                    info, erro = extrair_informacoes(numero_formatado, html_result)

                    if erro:
                        print(f"Erro persistente: {erro}")
                        continue
                else:
                    print("Não foi possível resolver o captcha. Continuando com o próximo número...")
                    continue

        # Salvar o resultado
        if info:
            print(info["linha"])
            if salvar_resultado(info):
                consultas_ok += 1

        # Pausa maior entre as consultas para não sobrecarregar o servidor
        if i < len(numeros) - 1:
            pausa = 5 + (i % 3)  # Pausa variável para evitar detecção de padrão
            print(f"Aguardando {pausa} segundos antes da próxima consulta...")
            time.sleep(pausa)

    print(f"\nProcessamento concluído. {consultas_ok}/{len(numeros)} consultas realizadas com sucesso.")
    print(f"Os resultados foram salvos no diretório '{DIRETORIO_RESULTADOS}'.")

if __name__ == "__main__":
    main()
