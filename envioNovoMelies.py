import requests


def enviarEmail():
    headers = {
        'Host': 'api.eatapp.co',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.5',
        'referer': 'https://app.eatapp.co/',
        'content-type': 'application/json',
        'x-restaurant-id': 'bc2654d8-53fe-49c6-b68a-b14ac579727e',
        'authorization': 'Bearer  eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDgxMjgzMTQsImlhdCI6MTc0NTUzNjMxNCwiaWQiOiI4MmE1MjFhMy05MjFjLTQ5MjItOWE4OC02NzhkYTAwZjJiMGYiLCJtb2RlbCI6IlJlc3RhdXJhbnRVc2VyIiwianRpIjoiNmMzOGZiYWQtN2NiYi00NWVkLTk4YTktNTNmMjU1MTFkMDVlIiwicm9sZSI6Im1hbmFnZXIifQ.phl8gc_XBvl1s77uFkIqJ5HAUd01znFFdOrc8T-nifc',
        'x-user-agent': 'cactus 5.9.4',
        'origin': 'https://app.eatapp.co',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'priority': 'u=0',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
    }

    data = {
        "text": "<!DOCTYPE html> <html lang=\"pt-BR\"> <head>     <meta charset=\"UTF-8\">     <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">     <title>Alerta de Infração - Documento Pessoal</title> </head> <body style=\"font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4;\">    TESTE ENVIO </body> </html>",
        "reservation_id": "41daeeb1-443d-4c2e-a0cb-c29bb436f8b7"
    }

    response = requests.post('https://api.eatapp.co/messaging/v2/emails', headers=headers, json=data)
    print(response.text)


def criarCliente():
    headers = {
        'Host': 'api.eatapp.co',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.5',
        'referer': 'https://app.eatapp.co/',
        'content-type': 'application/json',
        'x-restaurant-id': 'bc2654d8-53fe-49c6-b68a-b14ac579727e',
        'authorization': 'Bearer  eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDgxMjgzMTQsImlhdCI6MTc0NTUzNjMxNCwiaWQiOiI4MmE1MjFhMy05MjFjLTQ5MjItOWE4OC02NzhkYTAwZjJiMGYiLCJtb2RlbCI6IlJlc3RhdXJhbnRVc2VyIiwianRpIjoiNmMzOGZiYWQtN2NiYi00NWVkLTk4YTktNTNmMjU1MTFkMDVlIiwicm9sZSI6Im1hbmFnZXIifQ.phl8gc_XBvl1s77uFkIqJ5HAUd01znFFdOrc8T-nifc',
        'x-user-agent': 'cactus 5.9.4',
        'origin': 'https://app.eatapp.co',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'priority': 'u=0',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
    }

    json_data = {
        'id': '',
        'anniversary': '',
        'birthday': '',
        'email': '<EMAIL>',
        'first_name': 'Samuel Santana',
        'last_name': '',
        'notes': '',
        'phone': '5562984555484',
        'phoneError': False,
        'add_tags': [],
        'remove_tags': [],
        'alt_phone': '',
        'alt_email': '',
        'title': '',
        'organisation': '',
        'job_title': '',
        'gender': None,
        'loyalty_id': '',
        'address': '',
        'recentCallerId': None,
        'taggings': [],
        'custom_fields': None,
        'marketing_accepted': True,
    }

    response = requests.post('https://api.eatapp.co/restaurant/v2/guests', headers=headers, json=json_data)
    clienteId =  response.json()['data']['id']
    print(clienteId)
    return clienteId

def atualizarEmail(clienteId):
    headers = {
        'Host': 'api.eatapp.co',
        'user-agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0',
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'en-US,en;q=0.5',
        'referer': 'https://app.eatapp.co/',
        'content-type': 'application/json',
        'x-restaurant-id': 'bc2654d8-53fe-49c6-b68a-b14ac579727e',
        'authorization': 'Bearer  eyJhbGciOiJIUzI1NiJ9.eyJleHAiOjE3NDgxMjgzMTQsImlhdCI6MTc0NTUzNjMxNCwiaWQiOiI4MmE1MjFhMy05MjFjLTQ5MjItOWE4OC02NzhkYTAwZjJiMGYiLCJtb2RlbCI6IlJlc3RhdXJhbnRVc2VyIiwianRpIjoiNmMzOGZiYWQtN2NiYi00NWVkLTk4YTktNTNmMjU1MTFkMDVlIiwicm9sZSI6Im1hbmFnZXIifQ.phl8gc_XBvl1s77uFkIqJ5HAUd01znFFdOrc8T-nifc',
        'x-user-agent': 'cactus 5.9.4',
        'origin': 'https://app.eatapp.co',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'priority': 'u=4',
        # Requests doesn't support trailers
        # 'te': 'trailers',
        'pragma': 'no-cache',
        'cache-control': 'no-cache',
    }

    json_data = {
        'email': '<EMAIL>',
        'first_name': 'Samuel Santana',
        'last_name': None,
        'notes': None,
        'phone': '+5562984555484',
        'pos_data': {},
        'visit_data': {
            'last_visit': None,
            'next_visit': '2025-04-25T01:30:00',
            'first_visit': None,
            'visit_count': 0,
            'denied_count': 0,
            'visit_covers': 0,
            'denied_covers': 0,
            'next_visit_id': 'e1957d74-a652-4a99-ac87-3a3c79c47398',
            'no_show_count': 0,
            'reviews_count': 0,
            'canceled_count': 0,
            'no_show_covers': 0,
            'canceled_covers': 0,
            'reviews_average_rating': 0,
            'reviews_average_food_rating': 0,
            'upcoming_reservations_count': 1,
            'reviews_average_service_rating': 0,
            'reviews_average_ambience_rating': 0,
        },
        'visit_count': 0,
        'cancellation_count': 0,
        'no_show_count': 0,
        'imported_reservation_count': 0,
        'profile_image_id': None,
        'profile_image_url': None,
        'alt_email': None,
        'alt_phone': None,
        'title': None,
        'gender': None,
        'organisation': None,
        'job_title': None,
        'profession': None,
        'address': None,
        'loyalty_sid': None,
        'restaurant_id': 'bc2654d8-53fe-49c6-b68a-b14ac579727e',
        'shared_key': '8e5c30bf-030d-4c6b-b623-7e055c89b79a',
        'custom_fields': {},
        'demo': None,
        'loyalty_points': 0,
        'locale': None,
        'pms_data': {},
        'marketing_accepted': True,
        'created_at': '2025-04-24T18:27:50-05:00',
        'updated_at': '2025-04-24T18:27:51-05:00',
        'birthday': None,
        'anniversary': None,
        'tagging_ids': [],
        'taggings': [],
        'guest_ltv_id': 'ad27b492-9849-45ee-a55b-205aa688ccf3',
        'guest_ltv': {
            'id': 'ad27b492-9849-45ee-a55b-205aa688ccf3',
            'type': 'guest_ltv',
        },
        'voucher_assignment_ids': [],
        'voucher_assignments': [],
        'add_tags': [],
        'remove_tags': [],
        'find_duplicate': False,
    }

    response = requests.put(
        'https://api.eatapp.co/restaurant/v2/guests/ad27b492-9849-45ee-a55b-205aa688ccf3',
        headers=headers,
        json=json_data,
    )
    print(response.status_code)
    print(response.text)

sendEmail