from PIL import Image
import io
import base64
import sys

def render_base64(image_base64: str):
    """
    Renderiza uma imagem a partir de uma string base64.
    
    Args:
        image_base64 (str): String base64 contendo os dados da imagem
    """
    try:
        # Remove o cabeçalho se presente (ex: "data:image/png;base64,")
        if ',' in image_base64:
            image_base64 = image_base64.split(',')[1]
            
        # Decodifica o base64 para bytes
        image_data = base64.b64decode(image_base64)
        
        # Cria um objeto de imagem a partir dos bytes
        image = Image.open(io.BytesIO(image_data))
        
        # Exibe a imagem
        image.show()
        
    except Exception as e:
        print(f"Erro ao renderizar imagem: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Uso: python render_base64.py <base64_string>")
        sys.exit(1)
        
    render_base64(sys.argv[1])
